<script setup lang="ts">
import MarketTable from "../components/MarketTable.vue";
import MarketPriceTrend from "../components/MarketPriceTrend.vue";
</script>

<template>
  <div
    class="shadow-glow text-ocean-primary bg-ocean-gradient-middle h-full flex flex-col overflow-hidden"
  >
    <div class="title">银海集团南美白对虾价格指数</div>
    <div class="grid grid-cols-3 h-0 flex-1 gap-5 p-3">
      <div class="h-full flex flex-col">
        <div class="relative overflow-hidden">
          <img
            class="absolute inset-0 h-full w-full"
            src="@/assets/images/main_top_bottom.png"
          />
          <h3 class="pb-2 pl-5 pt-4">
            <span class="text-lg">今日市场价格</span>
            <span class="text-sm">(元/公斤)</span>
          </h3>
          <ul class="grid grid-cols-3 gap-4 px-5 pb-5">
            <li class="flex justify-center bg-#fff/5 p-4">
              <div>
                <div>规格：</div>
                <div>80只/公斤</div>
                <div>
                  <span class="text-ocean-accent pl-1rem text-2xl font-bold">
                    60
                  </span>
                  <span class="ml-1 text-xs">元/公斤</span>
                </div>
              </div>
            </li>
            <li class="flex justify-center bg-#fff/5 p-4">
              <div>
                <div>规格：</div>
                <div>60只/公斤</div>
                <div>
                  <span class="text-ocean-accent pl-1rem text-2xl font-bold">
                    70
                  </span>
                  <span class="ml-1 text-xs">元/公斤</span>
                </div>
              </div>
            </li>
            <li class="flex justify-center bg-#fff/5 p-4">
              <div>
                <div>规格：</div>
                <div>40只/公斤</div>
                <div>
                  <span class="text-ocean-accent pl-1rem text-2xl font-bold">
                    80
                  </span>
                  <span class="ml-1 text-xs">元/公斤</span>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <div class="relative mt-3 h-0 flex flex-1 flex-col overflow-hidden">
          <img
            class="absolute inset-0 h-full w-full"
            src="@/assets/images/main_top_bottom.png"
          />
          <h3 class="pb-2 pl-5 pt-4">
            <span class="text-lg">今日市场行情</span>
            <span class="text-sm">(元/公斤)</span>
          </h3>
          <div class="relative h-0 flex-auto overflow-hidden px-5 pb-5">
            <MarketTable />
          </div>
        </div>
        <div class="relative mt-3 h-0 flex flex-1 flex-col overflow-hidden">
          <img
            class="absolute inset-0 h-full w-full"
            src="@/assets/images/main_top_bottom.png"
          />
          <h3 class="pb-2 pl-5 pt-4">
            <span class="text-lg">市场价格</span>
            <span class="text-sm">(元/公斤)</span>
            <span class="ml-4 border border-#0efcff/50 px-3 text-sm">
              市场价格近一周走势
            </span>
          </h3>

          <div class="z-2 h-0 flex-auto overflow-hidden px-5 pb-5">
            <MarketPriceTrend />
          </div>
        </div>
      </div>
      <div class="h-full border">center</div>
      <div class="h-full border">right</div>
    </div>
  </div>
</template>

<style lang="scss">
.title {
  background: url("@/assets/images/title_bg.png") no-repeat;
  height: 80px;
  line-height: 80px;
  width: 1034px;
  background-size: cover;
  text-align: center;
  font-size: 40px;
  font-weight: bold;
  color: #0efcff;
  margin: 0 auto;
}

.liIn {
  border: 0.008rem solid rgba(14, 253, 255, 0.5);
  width: 100%;
  min-height: 60px;
  position: relative;
  width: 310px;
}

.border_bg_leftTop {
  position: absolute;
  left: -0.008rem;
  top: -0.04rem;
  width: 0.37rem;
  height: 0.05rem;
  display: block;
  background: #01279d url(./assets/images/title_left_bg.png) no-repeat;
  background-size: cover;
}

.border_bg_rightTop {
  position: absolute;
  right: -0.01rem;
  top: -0.01rem;
  width: 0.1rem;
  height: 0.1rem;
  display: block;
  background: url(./assets/images/border_bg.jpg) no-repeat;
  background-size: cover;
}

.border_bg_leftBottom {
  position: absolute;
  left: -0.008rem;
  bottom: -0.008rem;
  width: 0.1rem;
  height: 0.1rem;
  display: block;
  background: url(./assets/images/border_bg.jpg) no-repeat;
  background-size: cover;
}

.border_bg_rightBottom {
  position: absolute;
  right: -0.01rem;
  bottom: -0.01rem;
  width: 0.08rem;
  height: 0.08rem;
  display: block;
  background: url(./assets/images/title_right_bg.png) no-repeat;
  background-size: cover;
}
</style>
